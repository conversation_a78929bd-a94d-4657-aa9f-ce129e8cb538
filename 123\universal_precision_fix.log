2025-07-31 10:04:12,288 [INFO] 🚀 启动通用精度系统修复...
2025-07-31 10:04:12,289 [INFO] 🔧 应用通用系统修复...
2025-07-31 10:04:12,290 [INFO] 🔍 分析通用精度系统根本问题...
2025-07-31 10:04:12,290 [INFO] ✅ 识别到 3 个根本问题
2025-07-31 10:04:12,290 [INFO] 🔧 创建通用精度适配器...
2025-07-31 10:04:12,291 [ERROR] ❌ 通用系统修复失败: [Errno 2] No such file or directory: '123/core/universal_precision_adapter.py'
2025-07-31 10:04:12,292 [INFO] ✅ 通用精度系统修复完成
2025-07-31 10:04:42,869 [INFO] 🚀 启动通用精度系统修复...
2025-07-31 10:04:42,870 [INFO] 🔧 应用通用系统修复...
2025-07-31 10:04:42,871 [INFO] 🔍 分析通用精度系统根本问题...
2025-07-31 10:04:42,871 [INFO] ✅ 识别到 3 个根本问题
2025-07-31 10:04:42,871 [INFO] 🔧 创建通用精度适配器...
2025-07-31 10:04:42,872 [INFO] ✅ 通用精度适配器创建完成
2025-07-31 10:04:42,872 [INFO] 🔧 修复TradingRulesPreloader...
2025-07-31 10:04:42,873 [INFO] 🧪 验证通用修复效果...
2025-07-31 10:04:42,873 [INFO] ✅ 验证完成: 4个测试用例
2025-07-31 10:04:42,874 [INFO] 🎉 通用系统修复完成！
2025-07-31 10:04:42,875 [INFO] ✅ 通用精度系统修复完成
2025-07-31 10:05:05,257 [INFO] 🚀 启动通用精度系统修复...
2025-07-31 10:05:05,257 [INFO] 🔧 应用通用系统修复...
2025-07-31 10:05:05,257 [INFO] 🔍 分析通用精度系统根本问题...
2025-07-31 10:05:05,257 [INFO] ✅ 识别到 3 个根本问题
2025-07-31 10:05:05,257 [INFO] 🔧 创建通用精度适配器...
2025-07-31 10:05:05,258 [INFO] ✅ 通用精度适配器创建完成
2025-07-31 10:05:05,259 [INFO] 🔧 修复TradingRulesPreloader...
2025-07-31 10:05:05,259 [INFO] 🧪 验证通用修复效果...
2025-07-31 10:05:05,259 [INFO] ✅ 验证完成: 4个测试用例
2025-07-31 10:05:05,259 [INFO] 🎉 通用系统修复完成！
2025-07-31 10:05:05,261 [INFO] ✅ 通用精度系统修复完成
