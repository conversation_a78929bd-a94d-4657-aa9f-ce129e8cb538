2025-07-31 02:33:29,940 [INFO] 🔥 开始精度规则获取问题诊断
2025-07-31 02:33:29,940 [INFO] 🔍 诊断1: 检查全局交易所实例
2025-07-31 02:33:30,454 [INFO] TelegramNotifier初始化:
2025-07-31 02:33:30,454 [INFO]   Bot Token: 已设置
2025-07-31 02:33:30,454 [INFO]   Chat ID: 已设置
2025-07-31 02:33:30,455 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:33:30,455 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:33:30,456 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:33:30,472 [ERROR] ❌ 全局交易所实例为None
2025-07-31 02:33:30,473 [INFO] 🔍 诊断2: 检查TradingRulesPreloader异步调用
2025-07-31 02:33:30,474 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:33:30,474 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:33:30,475 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:33:30,475 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:33:30,475 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:33:30,475 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:33:30,476 [INFO]    缓存过期时间: 24小时
2025-07-31 02:33:30,476 [INFO]    预加载交易对数量: 10
2025-07-31 02:33:30,477 [INFO] 🔍 测试获取 ICNT-USDT bybit futures 精度规则
2025-07-31 02:33:30,477 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:33:30,477 [INFO] ================================================================================
2025-07-31 02:33:30,477 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:33:30,478 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:33:30,478 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:33:30,478 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:33:30,479 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:33:30,479 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:33:30,480 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:33:30,480 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:33:30,480 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:33:30,480 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:33:30,481 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:33:30,481 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:33:30,481 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:33:30,482 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:33:30,482 [INFO]    最大重试次数: 3
2025-07-31 02:33:30,482 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:33:30,482 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:33:30,484 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:33:30,484 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:33:30,485 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:33:30,485 [INFO]    🔥 最大重试: 3次
2025-07-31 02:33:30,485 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:33:30,486 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:33:30,486 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:33:30,486 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:33:30,487 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:33:30,488 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:33:30,489 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:33:30,489 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:33:30,489 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:33:30,490 [INFO] ✅ 同步获取成功: ICNT-USDT 步长=0.001
2025-07-31 02:33:30,490 [INFO] 🔍 测试获取 SPK-USDT bybit spot 精度规则
2025-07-31 02:33:30,491 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:33:30,491 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:33:30,492 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:33:30,492 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:33:30,492 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:33:30,493 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:33:30,493 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:33:30,493 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:33:30,493 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:33:30,494 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:33:30,494 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:33:30,495 [INFO] ✅ 同步获取成功: SPK-USDT 步长=0.001
2025-07-31 02:33:30,495 [INFO] 🔍 诊断3: 检查默认精度信息
2025-07-31 02:33:30,495 [INFO] Bybit默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 02:33:30,496 [INFO] 🔍 诊断4: 检查异步调用逻辑
2025-07-31 02:33:30,496 [INFO] ✅ 当前运行在异步环境中
2025-07-31 02:33:30,497 [INFO] ✅ 诊断完成，结果已保存到 async_architecture_diagnosis_results.json
2025-07-31 02:35:50,997 [INFO] 🔥 开始精度规则获取问题诊断
2025-07-31 02:35:50,997 [INFO] 🔍 诊断1: 检查全局交易所实例
2025-07-31 02:35:51,490 [INFO] TelegramNotifier初始化:
2025-07-31 02:35:51,490 [INFO]   Bot Token: 已设置
2025-07-31 02:35:51,490 [INFO]   Chat ID: 已设置
2025-07-31 02:35:51,490 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:35:51,491 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:35:51,491 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:35:51,508 [ERROR] ❌ 全局交易所实例为None
2025-07-31 02:35:51,508 [INFO] 🔍 诊断2: 检查TradingRulesPreloader异步调用
2025-07-31 02:35:51,509 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:35:51,510 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:35:51,510 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:35:51,510 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:35:51,511 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:35:51,511 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:35:51,512 [INFO]    缓存过期时间: 24小时
2025-07-31 02:35:51,512 [INFO]    预加载交易对数量: 10
2025-07-31 02:35:51,512 [INFO] 🔍 测试获取 ICNT-USDT bybit futures 精度规则
2025-07-31 02:35:51,512 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:35:51,512 [INFO] ================================================================================
2025-07-31 02:35:51,513 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:35:51,513 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:35:51,513 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:35:51,513 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:35:51,514 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:35:51,514 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:35:51,514 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:35:51,514 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:35:51,514 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:35:51,515 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:35:51,515 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:35:51,515 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:35:51,515 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:35:51,515 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:35:51,516 [INFO]    最大重试次数: 3
2025-07-31 02:35:51,516 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:35:51,516 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:35:51,519 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:35:51,520 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:35:51,520 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:35:51,520 [INFO]    🔥 最大重试: 3次
2025-07-31 02:35:51,521 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:35:51,521 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:35:51,522 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:35:51,522 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:35:51,522 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:35:51,523 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:35:51,524 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:35:51,524 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:35:51,524 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:35:51,525 [INFO] ✅ 同步获取成功: ICNT-USDT 步长=0.001
2025-07-31 02:35:51,525 [INFO] 🔍 测试获取 SPK-USDT bybit spot 精度规则
2025-07-31 02:35:51,525 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:35:51,525 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:35:51,526 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:35:51,526 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:35:51,526 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:35:51,527 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:35:51,527 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:35:51,527 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:35:51,528 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:35:51,528 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:35:51,528 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:35:51,528 [INFO] ✅ 同步获取成功: SPK-USDT 步长=0.001
2025-07-31 02:35:51,529 [INFO] 🔍 诊断3: 检查默认精度信息
2025-07-31 02:35:51,529 [INFO] Bybit默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 02:35:51,529 [INFO] 🔍 诊断4: 检查异步调用逻辑
2025-07-31 02:35:51,529 [INFO] ✅ 当前运行在异步环境中
2025-07-31 02:35:51,530 [INFO] ✅ 诊断完成，结果已保存到 async_architecture_diagnosis_results.json
2025-07-31 02:36:10,328 [INFO] 🔥 开始精度规则获取问题诊断
2025-07-31 02:36:10,329 [INFO] 🔍 诊断1: 检查全局交易所实例
2025-07-31 02:36:10,852 [INFO] TelegramNotifier初始化:
2025-07-31 02:36:10,852 [INFO]   Bot Token: 已设置
2025-07-31 02:36:10,852 [INFO]   Chat ID: 已设置
2025-07-31 02:36:10,853 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:36:10,853 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:36:10,853 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:36:10,869 [ERROR] ❌ 全局交易所实例为None
2025-07-31 02:36:10,869 [INFO] 🔍 诊断2: 检查TradingRulesPreloader异步调用
2025-07-31 02:36:10,870 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:36:10,871 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:36:10,871 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:36:10,871 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:36:10,872 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:36:10,872 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:36:10,872 [INFO]    缓存过期时间: 24小时
2025-07-31 02:36:10,873 [INFO]    预加载交易对数量: 10
2025-07-31 02:36:10,873 [INFO] 🔥 清除交易规则缓存，强制重新加载
2025-07-31 02:36:10,873 [INFO] 🔍 测试获取 ICNT-USDT bybit futures 精度规则
2025-07-31 02:36:10,874 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:36:10,874 [INFO] ================================================================================
2025-07-31 02:36:10,874 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:36:10,875 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:36:10,875 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:36:10,875 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:36:10,876 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:36:10,876 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:36:10,876 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:36:10,876 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:36:10,877 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:36:10,877 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:36:10,877 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:36:10,877 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:36:10,878 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:36:10,878 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:36:10,879 [INFO]    最大重试次数: 3
2025-07-31 02:36:10,879 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:36:10,879 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:10,881 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:36:10,881 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:36:10,881 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:36:10,882 [INFO]    🔥 最大重试: 3次
2025-07-31 02:36:10,882 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:36:10,883 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:36:10,883 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:36:10,883 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:36:10,883 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:10,884 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:36:10,885 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:36:10,885 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:36:10,885 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:36:10,885 [INFO] ✅ 同步获取成功: ICNT-USDT 步长=0.001
2025-07-31 02:36:10,886 [INFO] 🔍 测试获取 SPK-USDT bybit spot 精度规则
2025-07-31 02:36:10,886 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:36:10,886 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:36:10,886 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:36:10,886 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:36:10,886 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:36:10,887 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:36:10,887 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:36:10,887 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:10,887 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:36:10,887 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:36:10,888 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:36:10,888 [INFO] ✅ 同步获取成功: SPK-USDT 步长=0.001
2025-07-31 02:36:10,888 [INFO] 🔍 诊断3: 检查默认精度信息
2025-07-31 02:36:10,888 [INFO] Bybit默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 02:36:10,888 [INFO] 🔍 诊断4: 检查异步调用逻辑
2025-07-31 02:36:10,889 [INFO] ✅ 当前运行在异步环境中
2025-07-31 02:36:10,890 [INFO] ✅ 诊断完成，结果已保存到 async_architecture_diagnosis_results.json
2025-07-31 02:36:30,882 [INFO] 🔥 开始精度规则获取问题诊断
2025-07-31 02:36:30,882 [INFO] 🔍 诊断1: 检查全局交易所实例
2025-07-31 02:36:31,329 [INFO] TelegramNotifier初始化:
2025-07-31 02:36:31,329 [INFO]   Bot Token: 已设置
2025-07-31 02:36:31,329 [INFO]   Chat ID: 已设置
2025-07-31 02:36:31,330 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:36:31,330 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:36:31,330 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:36:31,346 [ERROR] ❌ 全局交易所实例为None
2025-07-31 02:36:31,346 [INFO] 🔍 诊断2: 检查TradingRulesPreloader异步调用
2025-07-31 02:36:31,347 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:36:31,348 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:36:31,348 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:36:31,349 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:36:31,349 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:36:31,350 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:36:31,350 [INFO]    缓存过期时间: 24小时
2025-07-31 02:36:31,350 [INFO]    预加载交易对数量: 10
2025-07-31 02:36:31,351 [INFO] 🔥 清除交易规则缓存，强制重新加载
2025-07-31 02:36:31,351 [INFO] 🔍 测试获取 ICNT-USDT bybit futures 精度规则
2025-07-31 02:36:31,351 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:36:31,352 [INFO] ================================================================================
2025-07-31 02:36:31,352 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:36:31,353 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:36:31,353 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:36:31,353 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:36:31,353 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:36:31,354 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:36:31,354 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:36:31,354 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:36:31,354 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:36:31,355 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:36:31,355 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:36:31,355 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:36:31,356 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:36:31,356 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:36:31,356 [INFO]    最大重试次数: 3
2025-07-31 02:36:31,356 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:36:31,356 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:31,358 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:36:31,358 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:36:31,359 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:36:31,359 [INFO]    🔥 最大重试: 3次
2025-07-31 02:36:31,359 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:36:31,360 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:36:31,360 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:36:31,360 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:36:31,361 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:31,362 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:36:31,362 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:36:31,363 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:36:31,363 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:36:31,363 [INFO] ✅ 同步获取成功: ICNT-USDT 步长=0.001
2025-07-31 02:36:31,363 [INFO] 🔍 测试获取 SPK-USDT bybit spot 精度规则
2025-07-31 02:36:31,364 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:36:31,364 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:36:31,364 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:36:31,364 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:36:31,364 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:36:31,365 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:36:31,365 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:36:31,365 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:36:31,366 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:36:31,366 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:36:31,366 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:36:31,366 [INFO] ✅ 同步获取成功: SPK-USDT 步长=0.001
2025-07-31 02:36:31,367 [INFO] 🔍 诊断3: 检查默认精度信息
2025-07-31 02:36:31,367 [INFO] Bybit通用默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 02:36:31,367 [INFO] ICNT-USDT期货默认精度: {'step_size': 1.0, 'min_amount': 1.0, 'max_amount': 250000, 'price_precision': 4, 'amount_precision': 0, 'min_notional': 5.0, 'source': 'default_corrected'}
2025-07-31 02:36:31,367 [INFO] SPK-USDT现货默认精度: {'step_size': 0.1, 'min_amount': 6.7, 'max_amount': 4598758, 'price_precision': 5, 'amount_precision': 1, 'min_notional': 5.0, 'source': 'default_corrected'}
2025-07-31 02:36:31,368 [INFO] 🔍 诊断4: 检查异步调用逻辑
2025-07-31 02:36:31,368 [INFO] ✅ 当前运行在异步环境中
2025-07-31 02:36:31,369 [INFO] ✅ 诊断完成，结果已保存到 async_architecture_diagnosis_results.json
2025-07-31 02:37:13,199 [INFO] 🔥 开始精度规则获取问题诊断
2025-07-31 02:37:13,200 [INFO] 🔍 诊断1: 检查全局交易所实例
2025-07-31 02:37:13,685 [INFO] TelegramNotifier初始化:
2025-07-31 02:37:13,686 [INFO]   Bot Token: 已设置
2025-07-31 02:37:13,686 [INFO]   Chat ID: 已设置
2025-07-31 02:37:13,686 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:37:13,686 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:37:13,686 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:37:13,703 [ERROR] ❌ 全局交易所实例为None
2025-07-31 02:37:13,704 [INFO] 🔍 诊断2: 检查TradingRulesPreloader异步调用
2025-07-31 02:37:13,704 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:37:13,705 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:37:13,705 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:37:13,706 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:37:13,706 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:37:13,706 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:37:13,707 [INFO]    缓存过期时间: 24小时
2025-07-31 02:37:13,707 [INFO]    预加载交易对数量: 10
2025-07-31 02:37:13,707 [INFO] 🔥 清除交易规则缓存，强制重新加载
2025-07-31 02:37:13,707 [INFO] 🔍 测试获取 ICNT-USDT bybit futures 精度规则
2025-07-31 02:37:13,707 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:37:13,708 [INFO] ================================================================================
2025-07-31 02:37:13,708 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:37:13,708 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:37:13,708 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:37:13,708 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:37:13,709 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:37:13,709 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:37:13,709 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:37:13,709 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:37:13,710 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:37:13,710 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:37:13,710 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:37:13,710 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:37:13,710 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:37:13,711 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:37:13,711 [INFO]    最大重试次数: 3
2025-07-31 02:37:13,711 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:37:13,711 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:37:13,713 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:37:13,713 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:37:13,713 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:37:13,714 [INFO]    🔥 最大重试: 3次
2025-07-31 02:37:13,714 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:37:13,715 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:37:13,715 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:37:13,715 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:37:13,716 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:37:13,717 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:37:13,717 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:37:13,717 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:37:13,718 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:37:13,718 [INFO] ✅ 同步获取成功: ICNT-USDT 步长=1.0
2025-07-31 02:37:13,718 [INFO] 🔍 测试获取 SPK-USDT bybit spot 精度规则
2025-07-31 02:37:13,718 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:37:13,719 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:37:13,719 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:37:13,719 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:37:13,719 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:37:13,720 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:37:13,720 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:37:13,720 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:37:13,721 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:37:13,721 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:37:13,722 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:37:13,722 [INFO] ✅ 同步获取成功: SPK-USDT 步长=0.1
2025-07-31 02:37:13,722 [INFO] 🔍 诊断3: 检查默认精度信息
2025-07-31 02:37:13,722 [INFO] Bybit通用默认精度: {'step_size': 0.001, 'min_amount': 0.001, 'max_amount': 1000000, 'price_precision': 2, 'amount_precision': 3, 'min_notional': 5.0, 'source': 'default'}
2025-07-31 02:37:13,723 [INFO] ICNT-USDT期货默认精度: {'step_size': 1.0, 'min_amount': 1.0, 'max_amount': 250000, 'price_precision': 4, 'amount_precision': 0, 'min_notional': 5.0, 'source': 'default_corrected'}
2025-07-31 02:37:13,723 [INFO] SPK-USDT现货默认精度: {'step_size': 0.1, 'min_amount': 6.7, 'max_amount': 4598758, 'price_precision': 5, 'amount_precision': 1, 'min_notional': 5.0, 'source': 'default_corrected'}
2025-07-31 02:37:13,723 [INFO] 🔍 诊断4: 检查异步调用逻辑
2025-07-31 02:37:13,724 [INFO] ✅ 当前运行在异步环境中
2025-07-31 02:37:13,725 [INFO] ✅ 诊断完成，结果已保存到 async_architecture_diagnosis_results.json
