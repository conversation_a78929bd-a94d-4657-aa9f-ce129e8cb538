#!/usr/bin/env python3
"""
🔥 通用精度适配器
支持任意代币的精度获取系统
"""

import logging
from typing import Dict, Any, Optional


class UniversalPrecisionAdapter:
    """🔥 通用精度适配器 - 支持任意代币的精度获取"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 🔥 交易所API字段映射 - 通用系统核心
        self.exchange_field_mapping = {
            "bybit": {
                "spot": {"step_field": "basePrecision", "min_field": "minOrderQty"},
                "futures": {"step_field": "qtyStep", "min_field": "minOrderQty"}
            },
            "gate": {
                "spot": {"step_field": "amount_precision", "min_field": "min_base_amount"},
                "futures": {"step_field": "order_size_min", "min_field": "order_size_min"}
            },
            "okx": {
                "spot": {"step_field": "lotSz", "min_field": "minSz"},
                "futures": {"step_field": "ctVal", "min_field": "minSz"}
            }
        }
    
    async def get_universal_precision(self, exchange_instance, exchange_name: str, symbol: str, market_type: str) -> Optional[Dict[str, Any]]:
        """🔥 通用精度获取 - 支持任意代币"""
        try:
            # 🚀 根据交易所类型调用相应的API
            if exchange_name == "bybit":
                return await self._get_bybit_precision(exchange_instance, symbol, market_type)
            elif exchange_name == "gate":
                return await self._get_gate_precision(exchange_instance, symbol, market_type)
            elif exchange_name == "okx":
                return await self._get_okx_precision(exchange_instance, symbol, market_type)
            else:
                self.logger.warning(f"⚠️ 不支持的交易所: {exchange_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 通用精度获取失败: {exchange_name} {symbol} {market_type} - {e}")
            return None
    
    async def _get_bybit_precision(self, exchange, symbol: str, market_type: str) -> Optional[Dict[str, Any]]:
        """获取Bybit精度信息"""
        try:
            category = "spot" if market_type == "spot" else "linear"
            instruments_info = await exchange.get_instruments_info(category=category, symbol=symbol)
            
            if instruments_info and "result" in instruments_info:
                instruments = instruments_info["result"]["list"]
                if len(instruments) > 0:
                    instrument = instruments[0]
                    lot_size_filter = instrument.get("lotSizeFilter", {})
                    
                    # 🔥 根据市场类型使用正确的字段
                    if market_type == "spot":
                        step_size = lot_size_filter.get("basePrecision", "0.001")
                    else:
                        step_size = lot_size_filter.get("qtyStep", "0.001")
                    
                    return {
                        "step_size": float(step_size),
                        "min_amount": float(lot_size_filter.get("minOrderQty", "0.001")),
                        "max_amount": float(lot_size_filter.get("maxOrderQty", "1000000")),
                        "source": "api_universal"
                    }
            return None
            
        except Exception as e:
            self.logger.debug(f"Bybit API调用失败: {e}")
            return None
    
    def get_intelligent_fallback_precision(self, exchange_name: str, symbol: str, market_type: str) -> Dict[str, Any]:
        """🔥 智能兜底精度 - 基于代币特征的通用算法"""
        
        # 🔥 通用算法：基于代币名称特征推断精度
        base_token = symbol.split('-')[0].upper()
        
        # 🔥 大市值代币通常使用较小步长
        large_cap_tokens = ['BTC', 'ETH', 'BNB', 'ADA', 'DOT', 'SOL', 'MATIC']
        # 🔥 小市值代币通常使用较大步长  
        small_cap_tokens = ['PEPE', 'SHIB', 'DOGE', 'WIF', 'BONK']
        # 🔥 整数代币（通常步长为1或0.1）
        integer_tokens = ['ICNT', 'CAKE', 'UNI', 'LINK', 'AAVE']
        
        if base_token in integer_tokens:
            # 整数代币使用1或0.1步长
            step_size = 1.0 if market_type == "futures" else 0.1
            amount_precision = 0 if step_size >= 1 else 1
        elif base_token in large_cap_tokens:
            # 大市值代币使用小步长
            step_size = 0.001
            amount_precision = 3
        elif base_token in small_cap_tokens:
            # 小市值代币使用更小步长
            step_size = 0.0001
            amount_precision = 4
        else:
            # 默认中等步长
            step_size = 0.01 if market_type == "spot" else 0.001
            amount_precision = 2 if market_type == "spot" else 3
        
        # 🔥 根据交易所调整
        if exchange_name == "gate":
            step_size *= 0.1  # Gate通常步长更小
            amount_precision += 1
        elif exchange_name == "okx":
            step_size *= 0.01  # OKX通常步长最小
            amount_precision += 2
        
        return {
            "step_size": step_size,
            "min_amount": step_size,
            "max_amount": 1000000,
            "price_precision": 4,
            "amount_precision": amount_precision,
            "min_notional": 5.0 if exchange_name == "bybit" else 1.0,
            "source": "intelligent_fallback"
        }
