2025-07-31 02:38:48,549 [INFO] 🎉 开始最终验证测试
2025-07-31 02:38:49,020 [INFO] TelegramNotifier初始化:
2025-07-31 02:38:49,020 [INFO]   Bot Token: 已设置
2025-07-31 02:38:49,021 [INFO]   Chat ID: 已设置
2025-07-31 02:38:49,021 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-31 02:38:49,022 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-31 02:38:49,023 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-31 02:38:49,053 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-31 02:38:49,054 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-31 02:38:49,054 [INFO] 📅 缓存时间: Thu Jul 31 02:02:58 2025
2025-07-31 02:38:49,055 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:38:49,055 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-31 02:38:49,055 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-31 02:38:49,055 [INFO]    缓存过期时间: 24小时
2025-07-31 02:38:49,056 [INFO]    预加载交易对数量: 10
2025-07-31 02:38:49,056 [INFO] 🧪 测试: ICNT-USDT期货精度规则
2025-07-31 02:38:49,056 [INFO] 🚀 5大缓存系统监控器启动
2025-07-31 02:38:49,056 [INFO] ================================================================================
2025-07-31 02:38:49,057 [INFO] 📋 [交易规则缓存] 未命中: bybit ICNT-USDT futures - 需要API获取
2025-07-31 02:38:49,057 [WARNING] ⚠️ 交易规则缓存未命中: bybit_ICNT-USDT_futures
2025-07-31 02:38:49,057 [INFO] 🔄 尝试动态加载交易规则: bybit_ICNT-USDT_futures
2025-07-31 02:38:49,057 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:38:49,058 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:38:49,058 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:38:49,058 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:38:49,058 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-31 02:38:49,058 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-31 02:38:49,059 [INFO]    🔥 步长缓存机制，最高速度
2025-07-31 02:38:49,059 [INFO]    🔥 严格截断，不四舍五入
2025-07-31 02:38:49,059 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-31 02:38:49,059 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-31 02:38:49,059 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-31 02:38:49,060 [INFO]    最大重试次数: 3
2025-07-31 02:38:49,060 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-31 02:38:49,060 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:38:49,062 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-31 02:38:49,062 [INFO]    🔥 连接超时: 5.0秒
2025-07-31 02:38:49,062 [INFO]    🔥 总超时: 10.0秒
2025-07-31 02:38:49,063 [INFO]    🔥 最大重试: 3次
2025-07-31 02:38:49,063 [INFO]    🔥 重试延迟: 50ms
2025-07-31 02:38:49,064 [INFO] 🔥 网络配置环境变量已应用
2025-07-31 02:38:49,064 [INFO] 📋 使用缓存的交易对: 10个
2025-07-31 02:38:49,064 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-31 02:38:49,065 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:38:49,067 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-31 02:38:49,067 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:38:49,068 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:38:49,068 [INFO] ✅ 同步加载成功: bybit_ICNT-USDT_futures
2025-07-31 02:38:49,068 [INFO] ✅ ICNT-USDT期货精度规则 - 测试通过
2025-07-31 02:38:49,068 [INFO] 🧪 测试: SPK-USDT现货精度规则
2025-07-31 02:38:49,069 [INFO] 📋 [交易规则缓存] 未命中: bybit SPK-USDT spot - 需要API获取
2025-07-31 02:38:49,069 [WARNING] ⚠️ 交易规则缓存未命中: bybit_SPK-USDT_spot
2025-07-31 02:38:49,069 [INFO] 🔄 尝试动态加载交易规则: bybit_SPK-USDT_spot
2025-07-31 02:38:49,069 [INFO] 🔄 尝试创建临时交易所实例: bybit
2025-07-31 02:38:49,070 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-31 02:38:49,070 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-31 02:38:49,070 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-31 02:38:49,070 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 02:38:49,070 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-31 02:38:49,071 [INFO] ✅ 临时交易所实例创建成功: bybit
2025-07-31 02:38:49,071 [INFO] ✅ 同步加载成功: bybit_SPK-USDT_spot
2025-07-31 02:38:49,071 [INFO] ✅ SPK-USDT现货精度规则 - 测试通过
2025-07-31 02:38:49,071 [INFO] 🎉 所有测试通过！精度规则修复完全成功！
2025-07-31 02:38:49,072 [INFO] ✅ 最终验证完成，结果已保存到 final_verification_results.json
